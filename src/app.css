@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global theme overrides - this will automatically fix all text colors */
@layer base {
  /* Light theme (default) */
  html {
    /* Override common text colors used in the app */
    --text-slate-200: rgb(75 85 99); /* gray-600 for better readability */
    --text-slate-100: rgb(17 24 39); /* gray-900 */
    --text-white: rgb(17 24 39); /* gray-900 */
    --text-white-70: rgb(75 85 99); /* gray-600 with opacity */
    --text-white-60: rgb(107 114 128); /* gray-500 */
    --text-white-50: rgb(156 163 175); /* gray-400 */
    --text-slate-200-90: rgb(55 65 81); /* gray-700 for overview text */
    --text-slate-200-70: rgb(107 114 128); /* gray-500 for cast character text */

    /* Override background colors for light theme */
    --bg-neutral-800-70: rgba(255, 255, 255, 0.9); /* white with slight transparency */
  }

  /* Dark theme */
  html.dark {
    /* Keep original colors for dark theme */
    --text-slate-200: rgb(226 232 240); /* slate-200 */
    --text-slate-100: rgb(241 245 249); /* slate-100 */
    --text-white: rgb(255 255 255); /* white */
    --text-white-70: rgba(255 255 255 0.7); /* white/70 */
    --text-white-60: rgba(255 255 255 0.6); /* white/60 */
    --text-white-50: rgba(255 255 255 0.5); /* white/50 */
    --text-slate-200-90: rgba(226 232 240 0.9); /* slate-200/90 */
    --text-slate-200-70: rgba(226 232 240 0.7); /* slate-200/70 */

    /* Keep original background colors for dark theme */
    --bg-neutral-800-70: rgba(38, 38, 38, 0.7); /* neutral-800/70 */
  }

  /* Apply the CSS variables to override Tailwind classes */
  .text-slate-200 {
    color: var(--text-slate-200) !important;
  }

  .text-slate-100 {
    color: var(--text-slate-100) !important;
  }

  .text-white {
    color: var(--text-white) !important;
  }

  .text-white\/70 {
    color: var(--text-white-70) !important;
  }

  .text-white\/60 {
    color: var(--text-white-60) !important;
  }

  .text-white\/50 {
    color: var(--text-white-50) !important;
  }

  .text-slate-200\/90 {
    color: var(--text-slate-200-90) !important;
  }

  .text-slate-200\/70 {
    color: var(--text-slate-200-70) !important;
  }

  /* Override background colors for theme switching */
  .bg-neutral-800\/70 {
    background-color: var(--bg-neutral-800-70) !important;
  }

  /* Exception: Always keep white text for elements with dark backgrounds */
  .always-white-text,
  .always-white-text * {
    color: white !important;
  }
}
