<script>
    import { fade } from 'svelte/transition';
    import PosterPlaceholder from './PosterPlaceholder.svelte';
    import { theme, getThemeClasses } from '$lib/stores/theme';

    export let title;
    export let description;
    export let year;

    // Reactive theme classes that update when theme changes
    $: cardPrimary = getThemeClasses('bg-white shadow-md', 'bg-neutral-800/70', $theme);
    $: cardSecondary = getThemeClasses('bg-gray-50', 'bg-neutral-900', $theme);
    $: textPrimary = getThemeClasses('text-gray-900', 'text-slate-100', $theme);
    $: textSecondary = getThemeClasses('text-gray-700', 'text-slate-200', $theme);
    $: textMuted = getThemeClasses('text-gray-500', 'text-white/70', $theme);
    $: backgroundOverlay = getThemeClasses('bg-white/90', 'bg-black/90', $theme);
</script>
<div in:fade|global class="relative flex flex-col md:flex-row {cardPrimary} p-6">
    <div class="{textMuted} flex items-center justify-center h-[250px] flex-none w-1/5 {cardSecondary}">
        <PosterPlaceholder />
    </div>
    <div class="md:hidden z-10 absolute inset-0 bg-cover bg-center">
        <div class="w-full h-full {backgroundOverlay} bg-blur-sm" />
    </div>

    <div class="z-40 flex flex-col justify-between md:ml-6">
        <div>
            <div class="flex items-end mb-4">
                <div title="Movie title" class="font-bold {textPrimary} text-3xl">
                    {title}
                    <span title="Release year" class="font-bold {textMuted} text-xl ml-2">{year}</span>
                </div>
            </div>
            <div title="Movie short plot/synopsis" class="{textSecondary} mb-4">
                {description}
            </div>
        </div>
    </div>
</div>