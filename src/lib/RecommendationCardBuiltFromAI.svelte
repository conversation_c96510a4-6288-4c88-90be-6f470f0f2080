<script>
    import { fade } from 'svelte/transition';
    import PosterPlaceholder from './PosterPlaceholder.svelte';
    import { themeClasses } from '$lib/stores/theme';

    export let title;
    export let description;
    export let year;
</script>
<div in:fade|global class="relative flex flex-col md:flex-row {themeClasses.card.primary} p-6">
    <div class="{themeClasses.text.muted} flex items-center justify-center h-[250px] flex-none w-1/5 {themeClasses.card.secondary}">
        <PosterPlaceholder />
    </div>
    <div class="md:hidden z-10 absolute inset-0 bg-cover bg-center">
        <div class="w-full h-full {themeClasses.background.overlay} bg-blur-sm" />
    </div>

    <div class="z-40 flex flex-col justify-between md:ml-6">
        <div>
            <div class="flex items-end mb-4">
                <div title="Movie title" class="font-bold {themeClasses.text.primary} text-3xl">
                    {title}
                    <span title="Release year" class="font-bold {themeClasses.text.muted} text-xl ml-2">{year}</span>
                </div>
            </div>
            <div title="Movie short plot/synopsis" class="{themeClasses.text.secondary} mb-4">
                {description}
            </div>
        </div>
    </div>
</div>