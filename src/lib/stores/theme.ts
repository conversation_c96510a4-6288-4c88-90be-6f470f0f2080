import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'light' | 'dark';

// Create the theme store with default value
function createThemeStore() {
	const { subscribe, set, update } = writable<Theme>('dark');

	return {
		subscribe,
		set,
		update,
		// Initialize theme from localStorage or system preference
		init: () => {
			if (!browser) return;
			
			const stored = localStorage.getItem('theme');
			if (stored && (stored === 'light' || stored === 'dark')) {
				set(stored as Theme);
				applyTheme(stored as Theme);
			} else {
				// Check system preference
				const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				const systemTheme: Theme = prefersDark ? 'dark' : 'light';
				set(systemTheme);
				applyTheme(systemTheme);
			}
		},
		// Toggle between light and dark
		toggle: () => {
			update(currentTheme => {
				const newTheme: Theme = currentTheme === 'dark' ? 'light' : 'dark';
				if (browser) {
					localStorage.setItem('theme', newTheme);
					applyTheme(newTheme);
				}
				return newTheme;
			});
		},
		// Set specific theme
		setTheme: (theme: Theme) => {
			set(theme);
			if (browser) {
				localStorage.setItem('theme', theme);
				applyTheme(theme);
			}
		}
	};
}

// Apply theme to document
function applyTheme(theme: Theme) {
	if (!browser) return;
	
	const root = document.documentElement;
	const body = document.body;
	
	if (theme === 'light') {
		root.classList.remove('dark');
		root.classList.add('light');
		body.classList.remove('bg-neutral-900', 'text-slate-100');
		body.classList.add('bg-gray-50', 'text-gray-900');
	} else {
		root.classList.remove('light');
		root.classList.add('dark');
		body.classList.remove('bg-gray-50', 'text-gray-900');
		body.classList.add('bg-neutral-900', 'text-slate-100');
	}
}

export const theme = createThemeStore();

// Theme-aware utility functions
export function getThemeClasses(lightClasses: string, darkClasses: string, currentTheme: Theme): string {
	return currentTheme === 'light' ? lightClasses : darkClasses;
}

// Common theme class combinations
export const themeClasses = {
	// Background classes
	background: {
		primary: 'bg-gray-50 dark:bg-neutral-900',
		secondary: 'bg-white dark:bg-neutral-800',
		tertiary: 'bg-gray-100 dark:bg-neutral-700',
		overlay: 'bg-white/90 dark:bg-black/90'
	},
	// Text classes
	text: {
		primary: 'text-gray-900 dark:text-slate-100',
		secondary: 'text-gray-700 dark:text-slate-200',
		muted: 'text-gray-500 dark:text-white/70',
		accent: 'text-turquoise-600 dark:text-turquoise-400'
	},
	// Border classes
	border: {
		primary: 'border-gray-200 dark:border-white/20',
		secondary: 'border-gray-300 dark:border-white/10'
	},
	// Button classes
	button: {
		primary: 'bg-turquoise-600 hover:bg-turquoise-700 text-white',
		secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-white',
		danger: 'bg-red-600 hover:bg-red-700 text-white'
	},
	// Card classes
	card: {
		primary: 'bg-white dark:bg-neutral-800/70 shadow-md dark:shadow-none',
		secondary: 'bg-gray-50 dark:bg-neutral-900'
	}
};
