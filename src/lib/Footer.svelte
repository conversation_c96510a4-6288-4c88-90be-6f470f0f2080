<script>
	import { theme, getThemeClasses } from '$lib/stores/theme';

	// Reactive theme classes
	$: borderPrimary = getThemeClasses('border-gray-200', 'border-white/20', $theme);
	$: textMuted = getThemeClasses('text-gray-500', 'text-white/70', $theme);
	$: textAccent = getThemeClasses('text-turquoise-600 hover:text-turquoise-700', 'text-turquoise-400 hover:text-turquoise-300', $theme);
</script>

<div
	class="max-w-4xl mx-auto w-full py-8 mt-20 border-t {borderPrimary}"
>
	<!-- Attribution Section -->
	<div class="{textMuted} flex items-center justify-center mb-6">
		Powered by <span class="mx-1 font-bold"> OpenAI</span>
		and
		<span class="ml-1 font-bold">Vercel Edge Functions</span>.
	</div>

	<!-- Data Attribution -->
	<div class="flex items-center justify-center gap-6 mb-6">
		<div class="flex items-center gap-2">
			<span class="{textMuted} text-sm">Data provided by</span>
			<a href="https://www.themoviedb.org/" target="_blank" rel="noopener noreferrer" class="hover:opacity-80 transition-opacity">
				<img src="/tmdb.svg" alt="The Movie Database" class="h-4" />
			</a>
		</div>
		<div class="flex items-center gap-2">
			<span class="{textMuted} text-sm">and</span>
			<a href="https://www.justwatch.com/" target="_blank" rel="noopener noreferrer" class="hover:opacity-80 transition-opacity">
				<img src="/justwatch.webp" alt="JustWatch" class="h-4" />
			</a>
		</div>
	</div>

	<!-- Legal Links -->
	<div class="flex items-center justify-center gap-6 text-sm">
		<a href="/privacy" class="{textAccent} transition-colors">
			Privacy Policy
		</a>
		<span class="{textMuted}">•</span>
		<a href="/terms" class="{textAccent} transition-colors">
			Terms of Service
		</a>
		<span class="{textMuted}">•</span>
		<span class="{textMuted}">© {new Date().getFullYear()} Cinemated</span>
	</div>
</div>
