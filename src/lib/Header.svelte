<script lang="ts">
	import TvIcon from './TvIcon.svelte';
	import { createEventDispatcher } from 'svelte';
	import { goto } from '$app/navigation';
	import { theme, getThemeClasses } from '$lib/stores/theme';

	interface UserData {
		email: string;
		country_code: string;
		isAdmin?: boolean;
		userType?: string;
	}

	export let isLoggedIn: boolean = false;
	export let userData: UserData | null = null;

	const dispatch = createEventDispatcher();

	function handleLogout() {
		dispatch('logout');
	}

	function handleLogoClick() {
		if (isLoggedIn) {
			goto('/recommendations');
		} else {
			goto('/');
		}
	}

	function handleThemeToggle() {
		theme.toggle();
	}

	// Reactive theme classes
	$: borderPrimary = getThemeClasses('border-gray-200', 'border-white/20', $theme);
	$: textMuted = getThemeClasses('text-gray-500', 'text-white/70', $theme);
	$: textPrimary = getThemeClasses('text-gray-900', 'text-white', $theme);
	$: buttonSecondary = getThemeClasses('bg-gray-200 hover:bg-gray-300 text-gray-900', 'bg-neutral-700 hover:bg-neutral-600 text-white', $theme);
	$: buttonDanger = 'bg-red-600 hover:bg-red-700 text-white';
</script>

<div
	class="max-w-4xl mx-auto w-full flex md:flex-row flex-col items-center md:justify-between py-8 mb-12 border-b {borderPrimary}"
>
	<button on:click={handleLogoClick} class="flex items-center mb-4 md:mb-0 {textMuted} hover:{textPrimary} transition-colors cursor-pointer">
		<TvIcon />
		<div class="text-2xl md:text-xl font-bold ml-2">Cinemated</div>
	</button>

	<div class="flex items-center gap-4">
		{#if isLoggedIn && userData}
			<div class="{textMuted} text-sm">
				{userData.email} ({userData.country_code})
			</div>
			{#if userData.isAdmin}
				<a
					href="/admin"
					class="bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md px-2 py-2 text-sm"
				>
					Admin Panel
				</a>
			{/if}
		{/if}

		<!-- Theme Toggle Button -->
		<button
			on:click={handleThemeToggle}
			class="{buttonSecondary} rounded-md px-2 py-2 text-sm transition-colors"
			title="Toggle theme"
		>
			{#if $theme === 'dark'}
				<!-- Sun icon for light mode -->
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
				</svg>
			{:else}
				<!-- Moon icon for dark mode -->
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
				</svg>
			{/if}
		</button>

		{#if isLoggedIn && userData}
			<button
				on:click={handleLogout}
				class="{buttonDanger} font-medium rounded-md px-2 py-2 text-sm"
			>
				Logout
			</button>
		{/if}
	</div>
</div>
